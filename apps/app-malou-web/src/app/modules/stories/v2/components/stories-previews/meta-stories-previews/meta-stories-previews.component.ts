import { Date<PERSON><PERSON><PERSON>, Lower<PERSON><PERSON><PERSON>ip<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    effect,
    ElementRef,
    inject,
    input,
    OnDestroy,
    OnInit,
    output,
    signal,
    untracked,
    viewChild,
} from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { LazyLoadImageModule } from 'ng-lazyload-image';
import { BehaviorSubject, filter, interval, map, Observable, of, startWith, Subject, switchMap, timer } from 'rxjs';

import { IGAccount, isNotNil, MediaType, PostPublicationStatus, PostUserTag, TimeInMilliseconds } from '@malou-io/package-utils';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';
import { ImageViewerComponent } from ':modules/posts-v2/social-posts/components/image-viewer/image-viewer.component';
import { TagAccountControlComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/instagram-preview/tag-account-control/tag-account-control.component';
import { TagAccountV2Component } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/instagram-preview/tag-account/tag-account.component';
import { StoryItem } from ':modules/stories/v2/models/story-item';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplySelfPurePipe } from ':shared/pipes/apply-fn.pipe';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';
import { IncludesPipe } from ':shared/pipes/includes.pipe';
import { PluralTranslatePipe } from ':shared/pipes/plural-translate.pipe';
import { ShortTextPipe } from ':shared/pipes/short-text.pipe';

@Component({
    selector: 'app-meta-stories-previews',
    templateUrl: './meta-stories-previews.component.html',
    styleUrls: ['./meta-stories-previews.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        NgClass,
        NgStyle,
        NgTemplateOutlet,
        LazyLoadImageModule,
        MatIconModule,
        MatTooltipModule,
        TranslateModule,
        TagAccountV2Component,
        MalouSpinnerComponent,
        ApplySelfPurePipe,
        DatePipe,
        EnumTranslatePipe,
        IllustrationPathResolverPipe,
        ImagePathResolverPipe,
        IncludesPipe,
        LowerCasePipe,
        PluralTranslatePipe,
        ShortTextPipe,
        ImageViewerComponent,
    ],
    providers: [ImagePathResolverPipe],
})
export class MetaStoriesPreviewsComponent extends TagAccountControlComponent implements OnInit, AfterViewInit, OnDestroy {
    readonly stories = input.required<StoryItem[]>();
    readonly accountName = input<string>();
    readonly profilePictureUrl = input<string>();
    readonly showStatus = input<boolean>(true);
    readonly isReadonly = input.required<boolean>();
    readonly canAddTagAccount = input.required<boolean>();
    readonly userTagsList = input<(PostUserTag[] | null)[]>([]);
    readonly userTagsHistory = input<{ username: string; count: number; igAccount: IGAccount }[]>([]);
    readonly toggleMediaScroll$ = input<Observable<0 | 1>>();

    readonly addUserTag = output<{ index: number; userTag: PostUserTag }>();
    readonly removeUserTag = output<{ index: number; username: string }>();

    private readonly _imagePathResolverPipe = inject(ImagePathResolverPipe);
    private readonly _destroyRef = inject(DestroyRef);

    private readonly _videoMedia = viewChild<ElementRef<HTMLVideoElement>>('videoMedia');

    readonly addAccountAvailable = computed(() => this.canAddTagAccount() && !this.isReadonly() && this.storiesForPreview().length > 0);

    readonly currentStoryIdx = signal(0);

    readonly storiesForPreview = computed(() =>
        // We unwind the stories to have only one media per story
        this.stories().flatMap((story) => story.medias.map((media) => story.copyWith({ medias: [media] })))
    );

    readonly currentStory = computed(() => {
        const currentStoryIdx = this.currentStoryIdx();
        const stories = this.storiesForPreview();
        return currentStoryIdx < stories.length ? stories[currentStoryIdx] : undefined;
    });
    readonly currentStoryMedia = computed(() => this.currentStory()?.medias[0]);

    readonly showTaggedAccounts = signal(false);

    private readonly _storyPlayDate = signal<Date>(new Date());
    private readonly _interval$ = interval(50 * TimeInMilliseconds.MILLISECOND).pipe(startWith(0), takeUntilDestroyed(this._destroyRef));
    private readonly _interval = toSignal(this._interval$);

    readonly progress = computed(() => {
        const MAX_PROGRESS = 100;

        const currentStory = this.currentStory();
        if (!currentStory) {
            return 0;
        }
        const mediaDuration = currentStory.getMediaDuration(0) * TimeInMilliseconds.SECOND;
        const storyPlayDate = this._storyPlayDate();
        if (isNotNil(this._interval())) {
            const now = this._pauseDate() ?? new Date();
            const progressTime = now.getTime() - storyPlayDate.getTime();
            const progressPercentage = Math.round((progressTime / mediaDuration) * 100);
            return Math.min(progressPercentage, MAX_PROGRESS);
        }
        return 0;
    });
    readonly progressPercentage = computed(() => {
        const progress = this.progress();
        return `${progress}%`;
    });

    readonly userTags = computed(() => {
        const userTagsList = this.userTagsList();
        const currentMediaIndex = this.currentStoryIdx();
        return userTagsList[currentMediaIndex] ?? [];
    });

    readonly SvgIcon = SvgIcon;
    readonly MediaType = MediaType;
    readonly PostPublicationStatus = PostPublicationStatus;

    private readonly _mediaDelay$ = new BehaviorSubject<number>(0);
    private readonly _DEFAULT_DELAY = 5000; // in ms

    private readonly _pauseDate = signal<Date | null>(null);

    readonly toggleVideoPausePlay$ = new Subject<0 | 1>();

    constructor() {
        super();

        effect(() => {
            const currentStory = this.currentStory();
            this._storyPlayDate.set(new Date());
            if (!currentStory) {
                return;
            }
            if (currentStory.medias[0]?.type === MediaType.VIDEO) {
                this._mediaDelay$.next(0);
            } else {
                this._mediaDelay$.next(this._DEFAULT_DELAY);
            }
        });

        effect(() => {
            const toggleMediaScroll$ = this.toggleMediaScroll$();
            if (!toggleMediaScroll$) {
                return;
            }
            toggleMediaScroll$.pipe(takeUntilDestroyed(this._destroyRef)).subscribe((value) => {
                const pauseDate = untracked(this._pauseDate);
                if (value === 0 && !pauseDate) {
                    this.pauseMediaScroll();
                } else if (value === 1) {
                    this.playMediaScroll();
                }
                this.toggleVideoPausePlay$.next(value);
            });
        });
    }

    ngOnInit(): void {
        this._mediaDelay$
            .pipe(
                // emit 0 to pause timer
                filter(Boolean),
                switchMap((delay) => of(delay).pipe(switchMap((time) => timer(time).pipe(map(() => time)))))
            )
            .subscribe({
                next: (res) => {
                    // emit 1 when user slide manually. currentStoryIdx already updated
                    if (res !== 1 && this._pauseDate() === null) {
                        this.currentStoryIdx.update((oldValue) => (oldValue + 1) % this.storiesForPreview().length);
                    }
                },
            });
    }

    ngAfterViewInit(): void {
        document.addEventListener('mousemove', this.moveTooltip);
    }

    ngOnDestroy(): void {
        document.removeEventListener('mousemove', this.moveTooltip);
    }

    onVideoEnd(): void {
        this._mediaDelay$.next(2);
    }

    previousMedia(): void {
        if (this.currentStoryIdx() <= 0) {
            this.currentStoryIdx.set(this.storiesForPreview().length - 1);
        } else {
            this.currentStoryIdx.update((oldValue) => oldValue - 1);
        }
        this._mediaDelay$.next(1);
    }

    nextMedia(): void {
        this.currentStoryIdx.update((oldValue) => (oldValue + 1) % this.storiesForPreview().length);
        this._mediaDelay$.next(1);
    }

    onImgError(event: Event): void {
        (event.target as HTMLImageElement).src = this._imagePathResolverPipe.transform('default_post');
    }

    onVideoPlayPause(isPlaying: boolean): void {
        if (isPlaying) {
            this.playMediaScroll();
        } else {
            this.pauseMediaScroll();
        }
    }

    onShowTaggedAccounts(): void {
        this.pauseMediaScroll();
        this.showTaggedAccounts.set(true);
        this.closeAddTagAccount();
    }

    hideTaggedAccounts(): void {
        this.showTaggedAccounts.set(false);
        this.playMediaScroll();
    }

    addAccount(event: MatAutocompleteSelectedEvent): void {
        const username = event.option.value.username;
        const profilePicture = event.option.value.profile_picture_url;
        const previewContainer = this.previewContainer().nativeElement;
        const previewContainerRect = previewContainer.getBoundingClientRect();

        // We save the ratio of the position of the tag in the image.
        const x = this.tagPosition().left / previewContainerRect.width;
        const y = this.tagPosition().top / previewContainerRect.height;

        const userTagsList = this.userTagsList();
        const currentStoryIdx = this.currentStoryIdx();

        if (username.length && !userTagsList[currentStoryIdx]?.some((el) => el.username === username)) {
            this.addUserTag.emit({ index: currentStoryIdx, userTag: { x, y, username, profilePicture } });
        }
        this.tagControl.setValue('');
        this.closeAddTagAccount();
        this.playMediaScroll();
        document.removeEventListener('keyup', (ev) => this._hideTagContainerOnEscape(ev));
    }

    removeAccount(username: string): void {
        // When we remove the last account, or the second last account, we hide the tagged accounts modal
        // because we will have no more accounts to display or just one and then we don't use this modal (only for 2+ tagged accounts)
        if (this.userTags().length <= 2) {
            this.hideTaggedAccounts();
        }
        this.removeUserTag.emit({ index: this.currentStoryIdx(), username });
    }

    pauseMediaScroll(): void {
        this._pauseDate.set(new Date());
        this._pauseVideo();
        this.toggleVideoPausePlay$.next(0);
    }

    playMediaScroll(): void {
        const pauseDate = this._pauseDate();
        const storyPlayDate = this._storyPlayDate();

        if (!pauseDate) {
            return;
        }
        const timePlayed = pauseDate.getTime() - storyPlayDate.getTime();

        const now = new Date();
        const newStoryPlayDate = new Date(now.getTime() - timePlayed);
        this._storyPlayDate.set(newStoryPlayDate);

        const currentStory = this.currentStory();
        if (!currentStory) {
            return;
        }
        const mediaDuration = currentStory.getMediaDuration(0) * TimeInMilliseconds.SECOND;
        const remainingTimeToPlay = mediaDuration - timePlayed;

        this._pauseDate.set(null);
        if (currentStory.medias[0]?.type === MediaType.VIDEO) {
            this._playVideo();
        } else {
            this._mediaDelay$.next(remainingTimeToPlay);
        }
        this.toggleVideoPausePlay$.next(1);
    }

    // Override the TagAccountControlComponent method
    protected shouldTagUserWithoutCoordinates(): boolean {
        return true;
    }

    private _pauseVideo(): void {
        this._videoMedia()?.nativeElement?.pause();
    }

    private _playVideo(): void {
        this._videoMedia()?.nativeElement?.play();
    }
}
