import { Job } from 'agenda';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { newDbId, toDbId } from '@malou-io/package-models';
import { getNextOccurrence, PostPublicationStatus, RecurrentStoryFrequency, TimeInMilliseconds } from '@malou-io/package-utils';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { publishStoryOnPlatformValidator } from ':helpers/validators/jobs/posts-jobs.validators';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PublishStoryOnPlatformUseCase } from ':modules/posts/v2/use-cases/publish-story-on-platform/publish-story-on-platform.use-case';
import { StoriesRepository } from ':modules/stories/repository/stories.repository';
import { ScheduleStoryPublicationService } from ':modules/stories/services/schedule-story-publication.service';

@singleton()
export class PublishStoryOnPlatformJob extends GenericJobDefinition {
    constructor(
        private readonly _publishStoryOnPlatformUseCase: PublishStoryOnPlatformUseCase,
        private readonly _storiesRepository: StoriesRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _scheduleStoryPublicationService: ScheduleStoryPublicationService
    ) {
        super({
            agendaJobName: AgendaJobName.PUBLISH_STORY_ON_PLATFORM,
            lockLifetimeMs: 15 * TimeInMilliseconds.MINUTE,
            getLogMetadata: async (job: Job) => {
                const data = publishStoryOnPlatformValidator.parse(job.attrs.data);
                const { storyId } = data;
                const { restaurantId, author } = await this._storiesRepository.findOneOrFail({
                    filter: { _id: toDbId(storyId) },
                    projection: { restaurantId: 1, author: 1 },
                    options: { lean: true },
                });

                assert(restaurantId, 'Missing restaurantId on post');
                assert(author, 'Missing author on post');

                return {
                    restaurant: { id: restaurantId.toString() },
                    user: { id: author._id.toString() },
                };
            },
        });
    }

    async executeJob(job: Job): Promise<void> {
        const data = publishStoryOnPlatformValidator.parse(job.attrs.data);
        const storyIds = await this._duplicateStoryOnPlatforms(data.storyId);
        const promises: Promise<void>[] = [];
        for (const storyId of storyIds) {
            promises.push(
                this._asyncLocalStorageService.createStoreAndRun({}, async () => {
                    await this._publishStoryOnPlatformUseCase.execute(storyId);
                })
            );
        }
        // might not need to wait
        await Promise.allSettled(promises);
    }

    private async _duplicateStoryOnPlatforms(storyId: string): Promise<string[]> {
        const story = await this._storiesRepository.findOne({
            filter: { _id: toDbId(storyId) },
            options: { lean: true },
        });
        assert(story, 'Story not found');
        const createdStoryIds: string[] = [];
        for (const platformKey of story.keys) {
            const platform = await this._platformsRepository.findOne({
                filter: { restaurantId: story.restaurantId, key: platformKey },
                options: { lean: true },
            });
            assert(platform, 'Platform not found');
            const newStoryId = newDbId();
            await this._storiesRepository.create({
                data: {
                    ...story,
                    _id: newStoryId,
                    key: platformKey,
                    keys: [],
                    platformId: platform._id,
                    isPublishing: true,
                    published: PostPublicationStatus.PENDING,
                    recurrentStoryFrequency: RecurrentStoryFrequency.NONE, // delete recurrent story frequency for publishing and published stories
                    feedbackId: null, // close feedbacks when publishing stories
                },
                options: { lean: true },
            });
            createdStoryIds.push(newStoryId.toString());
        }

        if (!story.recurrentStoryFrequency || story.recurrentStoryFrequency === RecurrentStoryFrequency.NONE) {
            await this._storiesRepository.deleteOne({
                filter: { _id: story._id },
            });
        } else {
            assert(story.plannedPublicationDate, 'Missing plannedPublicationDate on story');
            const nextPlannedPublicationDate = getNextOccurrence(story.plannedPublicationDate, story.recurrentStoryFrequency);
            await this._storiesRepository.updateOne({
                filter: { _id: story._id },
                update: { plannedPublicationDate: nextPlannedPublicationDate },
            });
            await this._scheduleStoryPublicationService.scheduleStoryPublication(story._id.toString(), nextPlannedPublicationDate);
        }

        return createdStoryIds;
    }
}
